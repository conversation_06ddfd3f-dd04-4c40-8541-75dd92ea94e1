{"indexes": [{"v": {"$numberInt": "1"}, "key": {"_id": {"$numberInt": "1"}}, "name": "_id_", "ns": "test.appshares"}, {"v": {"$numberInt": "1"}, "unique": true, "key": {"refId": {"$numberInt": "1"}, "companyId": {"$numberInt": "1"}, "type": {"$numberInt": "1"}}, "name": "refId_1_companyId_1_type_1", "ns": "test.appshares"}, {"v": {"$numberInt": "1"}, "key": {"companyId": {"$numberInt": "1"}, "type": {"$numberInt": "1"}, "status": {"$numberInt": "1"}, "featured.order": {"$numberInt": "1"}, "createdAt": {"$numberInt": "-1"}}, "name": "appshares_filter_sort_index", "ns": "test.appshares"}, {"v": {"$numberInt": "1"}, "key": {"companyId": {"$numberInt": "1"}, "status": {"$numberInt": "1"}, "featured.enabled": {"$numberInt": "-1"}, "featured.order": {"$numberInt": "1"}, "order": {"$numberInt": "1"}, "createdAt": {"$numberInt": "-1"}}, "name": "compound_filter_sort_index", "ns": "test.appshares"}, {"v": {"$numberInt": "1"}, "key": {"featured.enabled": {"$numberInt": "-1"}, "featured.order": {"$numberInt": "1"}, "order": {"$numberInt": "1"}, "createdAt": {"$numberInt": "-1"}}, "name": "featured.enabled_-1_featured.order_1_order_1_createdAt_-1", "ns": "test.appshares"}], "collectionName": "appshares", "type": "collection"}