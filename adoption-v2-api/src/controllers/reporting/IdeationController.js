const mongoose = require("mongoose");
const FormResponse = require("../../models/FormResponse");
const UserModel = require("../../models/UserModel");
const CompanyModel = require("../../models/CompanyModel");
const ReportModel = require("../../models/ReportModel");

// Function to get label from form schema
const getFieldLabel = (formData, fieldName, value) => {
  if (!formData || !value || value.toString().trim() === "") return "N/A";

  // Find all fields from topics array - support both direct topics and nested language versions
  let allFields = [];

  // Try direct topics first
  if (formData.topics && Array.isArray(formData.topics)) {
    formData.topics.forEach((topic) => {
      if (topic.fields && Array.isArray(topic.fields)) {
        allFields = [...allFields, ...topic.fields];
      }
    });
  }

  // Try language-specific topics (en, de, etc.)
  if (allFields.length === 0 && formData.en && formData.en.topics) {
    formData.en.topics.forEach((topic) => {
      if (topic.fields && Array.isArray(topic.fields)) {
        allFields = [...allFields, ...topic.fields];
      }
    });
  }

  // Try other language versions if en not found
  if (allFields.length === 0) {
    ["de", "tr", "es", "fr"].forEach((lang) => {
      if (formData[lang] && formData[lang].topics) {
        formData[lang].topics.forEach((topic) => {
          if (topic.fields && Array.isArray(topic.fields)) {
            allFields = [...allFields, ...topic.fields];
          }
        });
        return; // Stop after finding first language version
      }
    });
  }

  // Find the field by name
  const field = allFields.find((f) => f.name === fieldName);
  if (!field || !field.options || !Array.isArray(field.options)) {
    return value || "N/A"; // Return original value if field or options not found
  }

  // Find the option by value
  const option = field.options.find((opt) => opt.value === value);
  return option ? option.label : value || "N/A";
};

// Hardcoded form schema for value-to-label mapping
const getFormSchema = () => {
  return {
    en: {
      topics: [
        {
          fields: [
            {
              name: "business_benefit",
              options: [
                { value: "1", label: "Better customer experience" },
                { value: "2", label: "Increase in revenues" },
                { value: "3", label: "Cost reduction" },
                { value: "4", label: "Better efficiency" },
                { value: "5", label: "Higher quality" },
                { value: "6", label: "Better internal decision making" },
                { value: "7", label: "Better employee satisfaction" },
                { value: "8", label: "Risk reduction" },
                { value: "9", label: "New business model" },
                { value: "10", label: "Other" },
              ],
            },
            {
              name: "business_area",
              options: [
                { value: "1", label: "Marketing" },
                { value: "2", label: "Sales" },
                { value: "3", label: "Finance" },
                { value: "4", label: "Customer care & after sales" },
                { value: "5", label: "HR" },
                { value: "6", label: "IT" },
                { value: "7", label: "R&D" },
                { value: "8", label: "Supply chain management" },
                { value: "9", label: "Production" },
                { value: "10", label: "Quality control" },
                { value: "11", label: "Maintenance" },
                { value: "12", label: "Operations & processing" },
                { value: "13", label: "Risk & compliance" },
                { value: "14", label: "The entire company" },
                { value: "15", label: "Other" },
              ],
            },
            {
              name: "time_saved_per_week",
              options: [
                { value: "1", label: "Up to 5 hours per week" },
                { value: "2", label: "5-10 hours per week" },
                { value: "3", label: "More than 10 hours per week" },
                { value: "4", label: "Unsure" },
              ],
            },
            {
              name: "department_or_overall_in_our_company",
              options: [
                { value: "1", label: "1-5" },
                { value: "2", label: "6-10" },
                { value: "3", label: "10-50" },
                { value: "4", label: "50-100" },
                { value: "5", label: "100-1'000" },
                { value: "6", label: ">1'000" },
                { value: "7", label: "Unsure / not applicable" },
              ],
            },
            {
              name: "business_impact",
              options: [
                { value: "1", label: "Low impact: <100'000 per year" },
                {
                  value: "2",
                  label: "Medium impact: 100'000-1'000'000 per year",
                },
                { value: "3", label: "High impact: 1-10 million per year" },
                {
                  value: "4",
                  label: "Very high impact: above 10 million per year",
                },
                { value: "5", label: "No quantitative business impact" },
                {
                  value: "6",
                  label: "Quantitative business impact hard to quantify",
                },
              ],
            },
            {
              name: "possible_hurdles",
              options: [
                { value: "1", label: "Regulatory or compliance risks" },
                { value: "2", label: "Financial risks or budget constraints" },
                { value: "3", label: "Technical dependencies and limitations" },
                {
                  value: "4",
                  label: "Market competition or customer adoption risks",
                },
                { value: "5", label: "Resource and staffing constraints" },
                { value: "6", label: "No significant hurdles or risks" },
                { value: "7", label: "Other" },
              ],
            },
          ],
        },
      ],
    },
  };
};

exports.generateIdeationReport = async ({ companyId }) => {
  try {
    // Check if companyId is valid
    if (!mongoose.Types.ObjectId.isValid(companyId)) {
      return {
        reportType: "Ideation Report",
        status: "error",
        message: "Invalid companyId format",
        data: null,
      };
    }

    // Get company information
    const company = await CompanyModel.findById(companyId);
    if (!company) {
      return {
        reportType: "Ideation Report",
        status: "error",
        message: "Company not found",
        data: null,
      };
    }

    // Fetch form schema for value-to-label mapping
    const IDEA_FORM_ID = "68022d252b735a8a2d9c6fde";
    let formSchema = null;
    try {
      formSchema = getFormSchema(); // Use hardcoded schema
    } catch (error) {
      console.warn(
        "Could not fetch form schema for label mapping:",
        error.message
      );
    }

    // Find all ideation form responses and populate user info with company
    const allIdeationResponses = await FormResponse.find({
      formType: "ideation",
    })
      .populate({
        path: "submittedBy",
        select: "email company journeyLevel",
        model: "Users",
      })
      .sort({ createdAt: -1 });

    // Filter responses by company - only include users from the specified company
    const ideationResponses = allIdeationResponses.filter((response) => {
      return (
        response.submittedBy &&
        response.submittedBy.company &&
        response.submittedBy.company.toString() === companyId.toString()
      );
    });

    // Process and format the data according to CSV structure
    const reportData = ideationResponses.map((response) => {
      const responseData = {
        createdAt: response.createdAt,
        userEmail: response.submittedBy?.email || "N/A",
        source: response.source || "N/A",
        journeyLevel: response.submittedBy?.journeyLevel?.name || "N/A", // Kullanıcının gerçek level'ı
      };

      // Map form responses to specific fields based on field names
      response.responses.forEach((field) => {
        const fieldName = field.name.toLowerCase().trim();

        // Yeni field name'lere göre mapping
        if (fieldName === "title") {
          responseData.titleOfIdea = field.value;
        } else if (fieldName === "description") {
          responseData.describeIdea = field.value;
        } else if (
          fieldName.includes("attachment") ||
          fieldName.includes("file")
        ) {
          responseData.attachment = field.value ? "1" : "0";
        } else if (fieldName === "business_benefit") {
          responseData.primaryBenefit = getFieldLabel(
            formSchema,
            fieldName,
            field.value
          );
        } else if (
          fieldName === "business_area" ||
          fieldName === "targeted_business_area" ||
          fieldName === "target_business_area"
        ) {
          responseData.targetedBusinessArea = getFieldLabel(
            formSchema,
            fieldName,
            field.value
          );
        } else if (
          fieldName === "field_1745186080710" ||
          fieldName === "time_saved" ||
          fieldName === "time_save_per_week" ||
          fieldName === "time_saved_per_week" ||
          (fieldName.includes("time") && fieldName.includes("week"))
        ) {
          responseData.timeSaved = getFieldLabel(
            formSchema,
            "time_saved_per_week",
            field.value
          );
        } else if (
          fieldName === "concrete_benefits" ||
          (fieldName.includes("field_") && fieldName.includes("1745186131937"))
        ) {
          // Bu field concrete benefits için kullanılıyor (textarea - no mapping needed)
          responseData.concreteBenefits = field.value;
        } else if (fieldName === "department_or_overall_in_our_company") {
          responseData.colleaguesBenefit = getFieldLabel(
            formSchema,
            fieldName,
            field.value
          );
        } else if (fieldName === "business_impact") {
          responseData.annualImpact = getFieldLabel(
            formSchema,
            fieldName,
            field.value
          );
        } else if (fieldName === "possible_hurdles") {
          responseData.hurdlesRisks = getFieldLabel(
            formSchema,
            fieldName,
            field.value
          );
        }

        // Eski field name'ler için backward compatibility + daha kapsamlı matching
        else if (fieldName.includes("title") && fieldName.includes("idea")) {
          responseData.titleOfIdea = field.value;
        } else if (
          fieldName.includes("describe") &&
          fieldName.includes("idea")
        ) {
          responseData.describeIdea = field.value;
        } else if (
          fieldName.includes("primary") &&
          fieldName.includes("benefit")
        ) {
          responseData.primaryBenefit = getFieldLabel(
            formSchema,
            fieldName,
            field.value
          );
        } else if (
          fieldName.includes("targeted") &&
          (fieldName.includes("business") || fieldName.includes("area"))
        ) {
          responseData.targetedBusinessArea = getFieldLabel(
            formSchema,
            fieldName,
            field.value
          );
        } else if (
          fieldName.includes("business") &&
          fieldName.includes("area") &&
          !fieldName.includes("targeted")
        ) {
          responseData.targetedBusinessArea = getFieldLabel(
            formSchema,
            fieldName,
            field.value
          );
        } else if (
          fieldName.includes("time") &&
          (fieldName.includes("save") ||
            fieldName.includes("week") ||
            fieldName.includes("personally"))
        ) {
          responseData.timeSaved = getFieldLabel(
            formSchema,
            fieldName,
            field.value
          );
        } else if (
          fieldName.includes("describe") &&
          fieldName.includes("benefits") &&
          fieldName.includes("concrete")
        ) {
          responseData.concreteBenefits = field.value; // textarea - no mapping needed
        } else if (
          fieldName.includes("concrete") &&
          fieldName.includes("benefits")
        ) {
          responseData.concreteBenefits = field.value; // textarea - no mapping needed
        } else if (
          fieldName.includes("colleagues") &&
          fieldName.includes("benefit")
        ) {
          responseData.colleaguesBenefit = getFieldLabel(
            formSchema,
            fieldName,
            field.value
          );
        } else if (
          fieldName.includes("annual") &&
          (fieldName.includes("financial") ||
            fieldName.includes("impact") ||
            fieldName.includes("business"))
        ) {
          responseData.annualImpact = getFieldLabel(
            formSchema,
            fieldName,
            field.value
          );
        } else if (
          fieldName.includes("hurdles") ||
          fieldName.includes("risks")
        ) {
          responseData.hurdlesRisks = getFieldLabel(
            formSchema,
            fieldName,
            field.value
          );
        }
        // Additional catch-all patterns for missing fields
        else if (
          fieldName.includes("area") &&
          !responseData.targetedBusinessArea
        ) {
          responseData.targetedBusinessArea = getFieldLabel(
            formSchema,
            fieldName,
            field.value
          );
        } else if (
          fieldName.includes("efficiency") ||
          fieldName.includes("productivity")
        ) {
          if (!responseData.timeSaved)
            responseData.timeSaved = getFieldLabel(
              formSchema,
              fieldName,
              field.value
            );
        }
      });

      // Set default values for missing fields
      responseData.titleOfIdea = responseData.titleOfIdea || "N/A";
      responseData.describeIdea = responseData.describeIdea || "N/A";
      responseData.attachment = responseData.attachment || "0";
      responseData.primaryBenefit = responseData.primaryBenefit || "N/A";
      responseData.targetedBusinessArea =
        responseData.targetedBusinessArea || "N/A";
      responseData.timeSaved = responseData.timeSaved || "N/A";
      responseData.concreteBenefits = responseData.concreteBenefits || "N/A";
      responseData.colleaguesBenefit = responseData.colleaguesBenefit || "N/A";
      responseData.annualImpact = responseData.annualImpact || "N/A";
      responseData.hurdlesRisks = responseData.hurdlesRisks || "N/A";

      return responseData;
    });

    // Generate summary statistics
    const summary = {
      totalSubmissions: reportData.length,
      uniqueUsers: [...new Set(reportData.map((item) => item.userEmail))]
        .length,
      userLevelBreakdown: {}, // Breakdown by user levels
      sourceBreakdown: {},
      primaryBenefitBreakdown: {},
      businessAreaBreakdown: {},
    };

    // Calculate breakdowns
    reportData.forEach((item) => {
      // User level breakdown (real user level)
      const userLevel = item.journeyLevel;
      summary.userLevelBreakdown[userLevel] =
        (summary.userLevelBreakdown[userLevel] || 0) + 1;

      // Source breakdown
      const source = item.source;
      summary.sourceBreakdown[source] =
        (summary.sourceBreakdown[source] || 0) + 1;

      // Primary benefit breakdown
      const benefit = item.primaryBenefit || "Not specified";
      summary.primaryBenefitBreakdown[benefit] =
        (summary.primaryBenefitBreakdown[benefit] || 0) + 1;

      // Business area breakdown
      const area = item.targetedBusinessArea || "Not specified";
      summary.businessAreaBreakdown[area] =
        (summary.businessAreaBreakdown[area] || 0) + 1;
    });

    // Add inside report model
    const report = await ReportModel.create({
      companyName: company.companyName,
      company: companyId,
      reportType: "ideation",
      downloadLink: "not available",
      reportData: {
        summary,
        submissions: reportData,
      },
      createdAt: new Date(),
    });

    return {
      companyName: company.companyName,
      company: companyId,
      reportType: "ideation",
      status: "success",
      downloadLink: "not available",
      message: "Success",
      data: {
        summary,
        submissions: reportData,
      },
    };
  } catch (err) {
    console.error("Error generating ideation report:", err);
    return {
      reportType: "Ideation Report",
      status: "error",
      message: "Internal Server Error",
      data: null,
    };
  }
};
