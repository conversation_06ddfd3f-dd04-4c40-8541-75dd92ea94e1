const mongoose = require("mongoose");
const FormResponse = require("../../models/FormResponse");
const UserModel = require("../../models/UserModel");
const CompanyModel = require("../../models/CompanyModel");
const ReportModel = require("../../models/ReportModel");
const axios = require("axios");

// Function to get label from form schema
const getFieldLabel = (formData, fieldName, value, responseFieldName = null) => {
  if (!value || value.toString().trim() === "") return "N/A";

  // Convert value to string for consistent comparison
  const stringValue = String(value).trim();

  // Hardcoded mapping for ideation form fields based on known values
  const fieldMappings = {
    // Business benefit mapping
    business_benefit: {
      "1": "Better customer experience",
      "2": "Increase in revenues",
      "3": "Cost reduction",
      "4": "Better efficiency",
      "5": "Higher quality",
      "6": "Better internal decision making",
      "7": "Better employee satisfaction",
      "8": "Risk reduction",
      "9": "New business model",
      "10": "Other"
    },
    // Business area mapping
    business_area: {
      "1": "Marketing",
      "2": "Sales",
      "3": "Finance",
      "4": "Customer care & after sales",
      "5": "HR",
      "6": "IT",
      "7": "R&D",
      "8": "Supply chain management",
      "9": "Production",
      "10": "Quality control",
      "11": "Maintenance",
      "12": "Operations & processing",
      "13": "Risk & compliance",
      "14": "The entire company",
      "15": "Other"
    },
    // Colleagues benefit mapping
    department_or_overall_in_our_company: {
      "1": "1-5",
      "2": "6-10",
      "3": "10-50",
      "4": "50-100",
      "5": "100-1'000",
      "6": ">1'000",
      "7": "Unsure / not applicable"
    },
    // Business impact mapping
    business_impact: {
      "1": "Low impact: <100'000 per year",
      "2": "Medium impact: 100'000-1'000'000 per year",
      "3": "High impact: 1-10 million per year",
      "4": "Very high impact: above 10 million per year",
      "5": "No quantitative business impact",
      "6": "Quantitative business impact hard to quantify"
    },
    // Possible hurdles mapping
    possible_hurdles: {
      "1": "Regulatory or compliance risks",
      "2": "Financial risks or budget constraints",
      "3": "Technical dependencies and limitations",
      "4": "Market competition or customer adoption risks",
      "5": "Resource and staffing constraints",
      "6": "No significant hurdles or risks",
      "7": "Other"
    },
    // Time saved mapping
    time_saved_per_week: {
      "1": "Up to 5 hours per week",
      "2": "5-10 hours per week",
      "3": "More than 10 hours per week",
      "4": "Unsure"
    }
  };

  // Check if we have a direct mapping for this field
  if (fieldMappings[fieldName] && fieldMappings[fieldName][stringValue]) {
    return fieldMappings[fieldName][stringValue];
  }

  // Helper function to find field in form data
  const findFieldInFormData = (data, searchFieldName, searchResponseFieldName = null) => {
    if (!data) return null;

    let allFields = [];

    // Try direct topics first
    if (data.topics && Array.isArray(data.topics)) {
      data.topics.forEach((topic) => {
        if (topic.fields && Array.isArray(topic.fields)) {
          allFields = [...allFields, ...topic.fields];
        }
      });
    }

    // Find field by name (exact match first)
    let field = allFields.find((f) => f.name === searchFieldName);

    // If not found and we have response field name, try to match by response field name
    if (!field && searchResponseFieldName) {
      // Try to find field by matching response field name with field labels or names
      field = allFields.find((f) => {
        if (!f.label) return false;
        const fieldLabel = f.label.toLowerCase().trim();
        const responseField = searchResponseFieldName.toLowerCase().trim();

        // Check if response field name contains key parts of the field label
        return fieldLabel.includes(responseField) || responseField.includes(fieldLabel);
      });
    }

    return field;
  };

  // Try to find field in different language versions
  let field = null;

  // Try English first
  if (formData.en) {
    field = findFieldInFormData(formData.en, fieldName, responseFieldName);
  }

  // Try German if not found in English
  if (!field && formData.de) {
    field = findFieldInFormData(formData.de, fieldName, responseFieldName);
  }

  // Try other languages
  if (!field) {
    ["tr", "es", "fr"].forEach((lang) => {
      if (!field && formData[lang]) {
        field = findFieldInFormData(formData[lang], fieldName, responseFieldName);
      }
    });
  }

  // If field found and has options, try to map the value
  if (field && field.options && Array.isArray(field.options)) {
    const option = field.options.find((opt) => opt.value === stringValue);
    if (option) {
      return option.label;
    }
  }

  // Fallback: try to find any field with matching options for this value
  if (formData.en || formData.de) {
    const languages = ['en', 'de', 'tr', 'es', 'fr'];

    for (const lang of languages) {
      if (!formData[lang]) continue;

      const langData = formData[lang];
      if (langData.topics && Array.isArray(langData.topics)) {
        for (const topic of langData.topics) {
          if (topic.fields && Array.isArray(topic.fields)) {
            for (const f of topic.fields) {
              if (f.options && Array.isArray(f.options)) {
                const option = f.options.find((opt) => opt.value === stringValue);
                if (option) {
                  return option.label;
                }
              }
            }
          }
        }
      }
    }
  }

  // Return original value if no mapping found
  const result = value || "N/A";
  console.log(`getFieldLabel result: "${result}"`);
  return result;
};

// Function to fetch form schema from CDS API
const getFormSchemaFromAPI = async (formId) => {
  try {
    const CDS_API_URL = process.env.CDS_API_URL || "http://localhost:3001";
    const CDS_API_KEY = process.env.CDS_API_KEY;

    console.log(`Fetching form schema from: ${CDS_API_URL}/forms/list/${formId}`);

    // Fetch form with both English and German translations
    const [enResponse, deResponse] = await Promise.allSettled([
      axios.get(`${CDS_API_URL}/forms/list/${formId}`, {
        headers: {
          "Content-Type": "application/json",
          ...(CDS_API_KEY && { "x-api-key": CDS_API_KEY }),
        },
        params: { lang: "en" },
        timeout: 10000,
      }),
      axios.get(`${CDS_API_URL}/forms/list/${formId}`, {
        headers: {
          "Content-Type": "application/json",
          ...(CDS_API_KEY && { "x-api-key": CDS_API_KEY }),
        },
        params: { lang: "de" },
        timeout: 10000,
      }),
    ]);

    const formSchema = {};

    // Process English form data
    if (enResponse.status === "fulfilled" && enResponse.value?.data?.data) {
      formSchema.en = enResponse.value.data.data;
    }

    // Process German form data
    if (deResponse.status === "fulfilled" && deResponse.value?.data?.data) {
      formSchema.de = deResponse.value.data.data;
    }

    return formSchema;
  } catch (error) {
    console.warn("Could not fetch form schema from API:", error.message);
    return null;
  }
};

exports.generateIdeationReport = async ({ companyId }) => {
  try {
    // Check if companyId is valid
    if (!mongoose.Types.ObjectId.isValid(companyId)) {
      return {
        reportType: "Ideation Report",
        status: "error",
        message: "Invalid companyId format",
        data: null,
      };
    }

    // Get company information
    const company = await CompanyModel.findById(companyId);
    if (!company) {
      return {
        reportType: "Ideation Report",
        status: "error",
        message: "Company not found",
        data: null,
      };
    }

    // Fetch form schema for value-to-label mapping
    const IDEA_FORM_ID = "68022d252b735a8a2d9c6fde";
    let formSchema = null;
    try {
      formSchema = await getFormSchemaFromAPI(IDEA_FORM_ID);
      console.log("Form schema fetched successfully:", formSchema ? "Yes" : "No");
      if (formSchema) {
        console.log("Available languages:", Object.keys(formSchema));
        if (formSchema.en && formSchema.en.topics) {
          console.log("English topics count:", formSchema.en.topics.length);
        }
        if (formSchema.de && formSchema.de.topics) {
          console.log("German topics count:", formSchema.de.topics.length);
        }
      }
    } catch (error) {
      console.warn(
        "Could not fetch form schema for label mapping:",
        error.message
      );
    }

    // Find all ideation form responses and populate user info with company
    const allIdeationResponses = await FormResponse.find({
      formType: "ideation",
    })
      .populate({
        path: "submittedBy",
        select: "email company journeyLevel",
        model: "Users",
      })
      .sort({ createdAt: -1 });

    // Filter responses by company - only include users from the specified company
    const ideationResponses = allIdeationResponses.filter((response) => {
      return (
        response.submittedBy &&
        response.submittedBy.company &&
        response.submittedBy.company.toString() === companyId.toString()
      );
    });

    // Process and format the data according to CSV structure
    const reportData = ideationResponses.map((response) => {
      const responseData = {
        createdAt: response.createdAt,
        userEmail: response.submittedBy?.email || "N/A",
        source: response.source || "N/A",
        journeyLevel: response.submittedBy?.journeyLevel?.name || "N/A", // Kullanıcının gerçek level'ı
      };

      // Map form responses to specific fields based on field names
      response.responses.forEach((field) => {
        const fieldName = field.name.toLowerCase().trim();

        // Yeni field name'lere göre mapping (English and German)
        if (
          fieldName === "title" ||
          fieldName === "titel ihrer idee" ||
          fieldName.includes("titel") && fieldName.includes("idee")
        ) {
          responseData.titleOfIdea = field.value;
        } else if (
          fieldName === "description" ||
          fieldName === "beschreiben sie ihre idee" ||
          fieldName.includes("beschreiben") && fieldName.includes("idee")
        ) {
          responseData.describeIdea = field.value;
        } else if (
          fieldName.includes("attachment") ||
          fieldName.includes("file")
        ) {
          responseData.attachment = field.value ? "1" : "0";
        } else if (
          fieldName === "business_benefit" ||
          fieldName.includes("business") && fieldName.includes("benefit") ||
          fieldName.includes("expected") && fieldName.includes("benefit") ||
          fieldName.includes("primary") && fieldName.includes("benefit") ||
          fieldName === "was ist der wichtigste erwartete business-nutzen ihrer idee? (bitte wählen sie nur eine nutzen-dimension als aus ihrer sicht wichtigste und dominanteste aus)" ||
          fieldName.includes("wichtigste") && fieldName.includes("business-nutzen") ||
          fieldName.includes("erwartete") && fieldName.includes("nutzen")
        ) {
          responseData.primaryBenefit = getFieldLabel(
            formSchema,
            "business_benefit",
            field.value,
            field.name
          );
        } else if (
          fieldName === "business_area" ||
          fieldName === "targeted_business_area" ||
          fieldName === "target_business_area" ||
          fieldName.includes("business") && fieldName.includes("area") ||
          fieldName.includes("targeted") && fieldName.includes("area") ||
          fieldName === "was ist der unternehmensbereich, auf den sich ihre idee primär auswirkt?" ||
          fieldName.includes("unternehmensbereich") ||
          fieldName.includes("auswirkt") && fieldName.includes("idee")
        ) {
          responseData.targetedBusinessArea = getFieldLabel(
            formSchema,
            "business_area",
            field.value,
            field.name
          );
        } else if (
          fieldName === "field_1745186080710" ||
          fieldName === "time_saved" ||
          fieldName === "time_save_per_week" ||
          fieldName === "time_saved_per_week" ||
          (fieldName.includes("time") && fieldName.includes("week")) ||
          (fieldName.includes("time") && fieldName.includes("save"))
        ) {
          responseData.timeSaved = getFieldLabel(
            formSchema,
            "time_saved_per_week",
            field.value,
            field.name
          );
        } else if (
          fieldName === "concrete_benefits" ||
          (fieldName.includes("field_") && fieldName.includes("1745186131937")) ||
          fieldName.includes("concrete") && fieldName.includes("benefit") ||
          fieldName.includes("describe") && fieldName.includes("benefit") ||
          fieldName === "bitte beschreiben sie den nutzen ihrer idee für unser unternehmen so konkret wie möglich." ||
          fieldName.includes("beschreiben") && fieldName.includes("nutzen") ||
          fieldName.includes("konkret") && fieldName.includes("möglich")
        ) {
          // Bu field concrete benefits için kullanılıyor (textarea - no mapping needed)
          responseData.concreteBenefits = field.value;
        } else if (
          fieldName === "department_or_overall_in_our_company" ||
          fieldName.includes("colleague") && fieldName.includes("benefit") ||
          fieldName.includes("team") && fieldName.includes("benefit") ||
          fieldName.includes("department") && fieldName.includes("benefit") ||
          fieldName.includes("many") && fieldName.includes("colleague") ||
          fieldName === "wie viele kollegen in ihrem team / ihrer abteilung oder insgesamt in unserem unternehmen könnten von dieser use case idee profitieren?" ||
          fieldName.includes("kollegen") && fieldName.includes("profitieren") ||
          fieldName.includes("viele") && fieldName.includes("kollegen")
        ) {
          responseData.colleaguesBenefit = getFieldLabel(
            formSchema,
            "department_or_overall_in_our_company",
            field.value,
            field.name
          );
        } else if (
          fieldName === "business_impact" ||
          fieldName.includes("impact") && fieldName.includes("annual") ||
          fieldName.includes("impact") && fieldName.includes("business") ||
          fieldName.includes("expected") && fieldName.includes("impact") ||
          fieldName === "wie hoch ist der erwartete jährliche business-nutzen ihrer idee aus jährlicher finanzieller sicht? (euro, chf oder usd)" ||
          fieldName.includes("jährliche") && fieldName.includes("business-nutzen") ||
          fieldName.includes("erwartete") && fieldName.includes("finanzieller")
        ) {
          responseData.annualImpact = getFieldLabel(
            formSchema,
            "business_impact",
            field.value,
            field.name
          );
        } else if (
          fieldName === "possible_hurdles" ||
          fieldName.includes("hurdle") ||
          fieldName.includes("risk") ||
          fieldName.includes("possible") && fieldName.includes("hurdle") ||
          fieldName === "was sind mögliche hürden oder risiken im zusammenhang mit ihrer idee?" ||
          fieldName.includes("mögliche") && fieldName.includes("hürden") ||
          fieldName.includes("risiken") && fieldName.includes("zusammenhang")
        ) {
          responseData.hurdlesRisks = getFieldLabel(
            formSchema,
            "possible_hurdles",
            field.value,
            field.name
          );
        }
        // Handle field names that might be stored with different patterns
        else if (
          fieldName.includes("benefit") &&
          !fieldName.includes("concrete") &&
          !fieldName.includes("colleagues") &&
          !responseData.primaryBenefit
        ) {
          responseData.primaryBenefit = getFieldLabel(
            formSchema,
            "business_benefit",
            field.value
          );
        } else if (
          (fieldName.includes("area") || fieldName.includes("department")) &&
          !fieldName.includes("colleagues") &&
          !responseData.targetedBusinessArea
        ) {
          responseData.targetedBusinessArea = getFieldLabel(
            formSchema,
            "business_area",
            field.value
          );
        } else if (
          fieldName.includes("impact") &&
          !fieldName.includes("time") &&
          !responseData.annualImpact
        ) {
          responseData.annualImpact = getFieldLabel(
            formSchema,
            "business_impact",
            field.value
          );
        } else if (
          (fieldName.includes("hurdle") || fieldName.includes("risk")) &&
          !responseData.hurdlesRisks
        ) {
          responseData.hurdlesRisks = getFieldLabel(
            formSchema,
            "possible_hurdles",
            field.value
          );
        } else if (
          fieldName.includes("colleague") &&
          !responseData.colleaguesBenefit
        ) {
          responseData.colleaguesBenefit = getFieldLabel(
            formSchema,
            "department_or_overall_in_our_company",
            field.value
          );
        }
        // Final fallback: if field value is numeric and field type is select/radio, try to map it
        else if (
          (field.type === 'select' || field.type === 'radio') &&
          /^\d+$/.test(String(field.value).trim())
        ) {
          // Try to guess the field type based on field name patterns and map accordingly
          if (fieldName.includes("benefit") && !fieldName.includes("concrete") && !responseData.primaryBenefit) {
            responseData.primaryBenefit = getFieldLabel(formSchema, "business_benefit", field.value);
          } else if ((fieldName.includes("area") || fieldName.includes("business")) && !responseData.targetedBusinessArea) {
            responseData.targetedBusinessArea = getFieldLabel(formSchema, "business_area", field.value);
          } else if (fieldName.includes("impact") && !responseData.annualImpact) {
            responseData.annualImpact = getFieldLabel(formSchema, "business_impact", field.value);
          } else if ((fieldName.includes("hurdle") || fieldName.includes("risk")) && !responseData.hurdlesRisks) {
            responseData.hurdlesRisks = getFieldLabel(formSchema, "possible_hurdles", field.value);
          } else if ((fieldName.includes("colleague") || fieldName.includes("team") || fieldName.includes("department")) && !responseData.colleaguesBenefit) {
            responseData.colleaguesBenefit = getFieldLabel(formSchema, "department_or_overall_in_our_company", field.value);
          } else if (fieldName.includes("time") && !responseData.timeSaved) {
            responseData.timeSaved = getFieldLabel(formSchema, "time_saved_per_week", field.value);
          }
        }
        // Additional fallback: try to map any numeric field that hasn't been mapped yet
        else if (/^\d+$/.test(String(field.value).trim())) {
          // If we have a numeric value but haven't mapped it yet, try all possible mappings
          if (!responseData.primaryBenefit) {
            const mappedValue = getFieldLabel(formSchema, "business_benefit", field.value);
            if (mappedValue !== field.value && mappedValue !== "N/A") {
              responseData.primaryBenefit = mappedValue;
            }
          }
          if (!responseData.targetedBusinessArea) {
            const mappedValue = getFieldLabel(formSchema, "business_area", field.value);
            if (mappedValue !== field.value && mappedValue !== "N/A") {
              responseData.targetedBusinessArea = mappedValue;
            }
          }
          if (!responseData.annualImpact) {
            const mappedValue = getFieldLabel(formSchema, "business_impact", field.value);
            if (mappedValue !== field.value && mappedValue !== "N/A") {
              responseData.annualImpact = mappedValue;
            }
          }
          if (!responseData.hurdlesRisks) {
            const mappedValue = getFieldLabel(formSchema, "possible_hurdles", field.value);
            if (mappedValue !== field.value && mappedValue !== "N/A") {
              responseData.hurdlesRisks = mappedValue;
            }
          }
          if (!responseData.colleaguesBenefit) {
            const mappedValue = getFieldLabel(formSchema, "department_or_overall_in_our_company", field.value);
            if (mappedValue !== field.value && mappedValue !== "N/A") {
              responseData.colleaguesBenefit = mappedValue;
            }
          }
          if (!responseData.timeSaved) {
            const mappedValue = getFieldLabel(formSchema, "time_saved_per_week", field.value);
            if (mappedValue !== field.value && mappedValue !== "N/A") {
              responseData.timeSaved = mappedValue;
            }
          }
        }
        // Handle specific field IDs that might be used in the database
        else if (fieldName.startsWith("field_")) {
          // Map known field IDs to their corresponding data fields
          if (/^\d+$/.test(String(field.value).trim())) {
            // Try to map based on field ID patterns
            if (!responseData.primaryBenefit) {
              const mappedValue = getFieldLabel(formSchema, "business_benefit", field.value);
              if (mappedValue !== field.value && mappedValue !== "N/A") {
                responseData.primaryBenefit = mappedValue;
              }
            }
            if (!responseData.targetedBusinessArea) {
              const mappedValue = getFieldLabel(formSchema, "business_area", field.value);
              if (mappedValue !== field.value && mappedValue !== "N/A") {
                responseData.targetedBusinessArea = mappedValue;
              }
            }
            if (!responseData.annualImpact) {
              const mappedValue = getFieldLabel(formSchema, "business_impact", field.value);
              if (mappedValue !== field.value && mappedValue !== "N/A") {
                responseData.annualImpact = mappedValue;
              }
            }
            if (!responseData.hurdlesRisks) {
              const mappedValue = getFieldLabel(formSchema, "possible_hurdles", field.value);
              if (mappedValue !== field.value && mappedValue !== "N/A") {
                responseData.hurdlesRisks = mappedValue;
              }
            }
            if (!responseData.colleaguesBenefit) {
              const mappedValue = getFieldLabel(formSchema, "department_or_overall_in_our_company", field.value);
              if (mappedValue !== field.value && mappedValue !== "N/A") {
                responseData.colleaguesBenefit = mappedValue;
              }
            }
          }
        }

        // Eski field name'ler için backward compatibility + daha kapsamlı matching
        else if (fieldName.includes("title") && fieldName.includes("idea")) {
          responseData.titleOfIdea = field.value;
        } else if (
          fieldName.includes("describe") &&
          fieldName.includes("idea")
        ) {
          responseData.describeIdea = field.value;
        } else if (
          fieldName.includes("primary") &&
          fieldName.includes("benefit")
        ) {
          responseData.primaryBenefit = getFieldLabel(
            formSchema,
            "business_benefit",
            field.value
          );
        } else if (
          fieldName.includes("targeted") &&
          (fieldName.includes("business") || fieldName.includes("area"))
        ) {
          responseData.targetedBusinessArea = getFieldLabel(
            formSchema,
            "business_area",
            field.value
          );
        } else if (
          fieldName.includes("business") &&
          fieldName.includes("area") &&
          !fieldName.includes("targeted")
        ) {
          responseData.targetedBusinessArea = getFieldLabel(
            formSchema,
            "business_area",
            field.value
          );
        } else if (
          fieldName.includes("time") &&
          (fieldName.includes("save") ||
            fieldName.includes("week") ||
            fieldName.includes("personally"))
        ) {
          responseData.timeSaved = getFieldLabel(
            formSchema,
            "time_saved_per_week",
            field.value
          );
        } else if (
          fieldName.includes("describe") &&
          fieldName.includes("benefits") &&
          fieldName.includes("concrete")
        ) {
          responseData.concreteBenefits = field.value; // textarea - no mapping needed
        } else if (
          fieldName.includes("concrete") &&
          fieldName.includes("benefits")
        ) {
          responseData.concreteBenefits = field.value; // textarea - no mapping needed
        } else if (
          fieldName.includes("colleagues") &&
          fieldName.includes("benefit")
        ) {
          responseData.colleaguesBenefit = getFieldLabel(
            formSchema,
            "department_or_overall_in_our_company",
            field.value
          );
        } else if (
          fieldName.includes("annual") &&
          (fieldName.includes("financial") ||
            fieldName.includes("impact") ||
            fieldName.includes("business"))
        ) {
          responseData.annualImpact = getFieldLabel(
            formSchema,
            "business_impact",
            field.value
          );
        } else if (
          fieldName.includes("hurdles") ||
          fieldName.includes("risks")
        ) {
          responseData.hurdlesRisks = getFieldLabel(
            formSchema,
            "possible_hurdles",
            field.value
          );
        }
        // Additional catch-all patterns for missing fields
        else if (
          fieldName.includes("area") &&
          !responseData.targetedBusinessArea
        ) {
          responseData.targetedBusinessArea = getFieldLabel(
            formSchema,
            "business_area",
            field.value
          );
        } else if (
          fieldName.includes("efficiency") ||
          fieldName.includes("productivity")
        ) {
          if (!responseData.timeSaved)
            responseData.timeSaved = getFieldLabel(
              formSchema,
              "time_saved_per_week",
              field.value
            );
        }
      });

      // Final pass: try to map any remaining numeric values that weren't caught
      response.responses.forEach((field) => {
        if (/^\d+$/.test(String(field.value).trim())) {
          const numericValue = String(field.value).trim();

          // Try to map to primaryBenefit if it's still N/A or empty
          if (!responseData.primaryBenefit || responseData.primaryBenefit === "N/A") {
            const mappedValue = getFieldLabel(formSchema, "business_benefit", numericValue);
            if (mappedValue !== numericValue && mappedValue !== "N/A") {
              responseData.primaryBenefit = mappedValue;
              return; // Found a mapping, move to next field
            }
          }

          // Try to map to targetedBusinessArea if it's still N/A or empty
          if (!responseData.targetedBusinessArea || responseData.targetedBusinessArea === "N/A") {
            const mappedValue = getFieldLabel(formSchema, "business_area", numericValue);
            if (mappedValue !== numericValue && mappedValue !== "N/A") {
              responseData.targetedBusinessArea = mappedValue;
              return; // Found a mapping, move to next field
            }
          }

          // Try to map to annualImpact if it's still N/A or empty
          if (!responseData.annualImpact || responseData.annualImpact === "N/A") {
            const mappedValue = getFieldLabel(formSchema, "business_impact", numericValue);
            if (mappedValue !== numericValue && mappedValue !== "N/A") {
              responseData.annualImpact = mappedValue;
              return; // Found a mapping, move to next field
            }
          }

          // Try to map to hurdlesRisks if it's still N/A or empty
          if (!responseData.hurdlesRisks || responseData.hurdlesRisks === "N/A") {
            const mappedValue = getFieldLabel(formSchema, "possible_hurdles", numericValue);
            if (mappedValue !== numericValue && mappedValue !== "N/A") {
              responseData.hurdlesRisks = mappedValue;
              return; // Found a mapping, move to next field
            }
          }

          // Try to map to colleaguesBenefit if it's still N/A or empty
          if (!responseData.colleaguesBenefit || responseData.colleaguesBenefit === "N/A") {
            const mappedValue = getFieldLabel(formSchema, "department_or_overall_in_our_company", numericValue);
            if (mappedValue !== numericValue && mappedValue !== "N/A") {
              responseData.colleaguesBenefit = mappedValue;
              return; // Found a mapping, move to next field
            }
          }

          // Try to map to timeSaved if it's still N/A or empty
          if (!responseData.timeSaved || responseData.timeSaved === "N/A") {
            const mappedValue = getFieldLabel(formSchema, "time_saved_per_week", numericValue);
            if (mappedValue !== numericValue && mappedValue !== "N/A") {
              responseData.timeSaved = mappedValue;
              return; // Found a mapping, move to next field
            }
          }
        }
      });

      // Set default values for missing fields
      responseData.titleOfIdea = responseData.titleOfIdea || "N/A";
      responseData.describeIdea = responseData.describeIdea || "N/A";
      responseData.attachment = responseData.attachment || "0";
      responseData.primaryBenefit = responseData.primaryBenefit || "N/A";
      responseData.targetedBusinessArea =
        responseData.targetedBusinessArea || "N/A";
      responseData.timeSaved = responseData.timeSaved || "N/A";
      responseData.concreteBenefits = responseData.concreteBenefits || "N/A";
      responseData.colleaguesBenefit = responseData.colleaguesBenefit || "N/A";
      responseData.annualImpact = responseData.annualImpact || "N/A";
      responseData.hurdlesRisks = responseData.hurdlesRisks || "N/A";

      return responseData;
    });

    // Generate summary statistics
    const summary = {
      totalSubmissions: reportData.length,
      uniqueUsers: [...new Set(reportData.map((item) => item.userEmail))]
        .length,
      userLevelBreakdown: {}, // Breakdown by user levels
      sourceBreakdown: {},
      primaryBenefitBreakdown: {},
      businessAreaBreakdown: {},
    };

    // Calculate breakdowns
    reportData.forEach((item) => {
      // User level breakdown (real user level)
      const userLevel = item.journeyLevel;
      summary.userLevelBreakdown[userLevel] =
        (summary.userLevelBreakdown[userLevel] || 0) + 1;

      // Source breakdown
      const source = item.source;
      summary.sourceBreakdown[source] =
        (summary.sourceBreakdown[source] || 0) + 1;

      // Primary benefit breakdown
      const benefit = item.primaryBenefit || "Not specified";
      summary.primaryBenefitBreakdown[benefit] =
        (summary.primaryBenefitBreakdown[benefit] || 0) + 1;

      // Business area breakdown
      const area = item.targetedBusinessArea || "Not specified";
      summary.businessAreaBreakdown[area] =
        (summary.businessAreaBreakdown[area] || 0) + 1;
    });

    // Add inside report model
    await ReportModel.create({
      companyName: company.companyName,
      company: companyId,
      reportType: "ideation",
      downloadLink: "not available",
      reportData: {
        summary,
        submissions: reportData,
      },
      createdAt: new Date(),
    });

    return {
      companyName: company.companyName,
      company: companyId,
      reportType: "ideation",
      status: "success",
      downloadLink: "not available",
      message: "Success",
      data: {
        summary,
        submissions: reportData,
      },
    };
  } catch (err) {
    console.error("Error generating ideation report:", err);
    return {
      reportType: "Ideation Report",
      status: "error",
      message: "Internal Server Error",
      data: null,
    };
  }
};
